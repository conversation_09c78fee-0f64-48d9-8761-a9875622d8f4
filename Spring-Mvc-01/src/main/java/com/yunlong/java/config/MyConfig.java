package com.yunlong.java.config;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.HandlerAdapter;
import org.springframework.web.servlet.HandlerMapping;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.ViewResolverRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

@Configuration
@EnableWebMvc//增加秘书和经理，并给经理增加解析json的功能，并把他俩放到ioc容器,就不用我们创建了
@ComponentScan(basePackages = {"com.yunlong.java.control","com.yunlong.java.error"})
public class MyConfig implements WebMvcConfigurer {
          public void configureViewResolvers(ViewResolverRegistry registry) {
              registry.jsp("/WEB-INF/views/", ".jsp");
          }
}
