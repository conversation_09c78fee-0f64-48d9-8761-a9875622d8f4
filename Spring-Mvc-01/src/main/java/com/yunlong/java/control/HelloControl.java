package com.yunlong.java.control;
import com.yunlong.java.pojo.User;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import java.util.List;
@Controller
@ResponseBody//作用是返回字符串，不找视图解析器
public class HelloControl {
    //必须使用get请求来访问
    @GetMapping(
            value = "/springmvc/hello1",
            produces = "text/plain;charset=UTF-8"// 显式设置UTF-8编码
    )

    public String hello1(@RequestParam(value ="account",required = false,defaultValue = "传参失败")String name,
                         @RequestParam(value ="aaa",required = false,defaultValue = "101") int age) {
        return "Hello " + name + " " + age;
    }


    //接受动态路径
    @RequestMapping("{account}/{aaa}")
    public String hello2(@PathVariable(value = "account",required = false) String name, @PathVariable(value = "aaa",required = false) int age) {
        return "Hello " + name + " " + age;
    }


    //接受json数据
    @PostMapping(
                  value = "/springmvc/hello3",
                  produces = "text/plain;charset=UTF-8"// 显式设置UTF-8编码)
    )
    public String hello3(@RequestBody User user) {
        return user.toString();
    }

    //返回json数据
    @GetMapping("helloJson")
    public User hello4(){
        User user = new User();
        user.setAge(10);
        user.setName("hhhh");
        return user;
    }

    @GetMapping("/text")
    public String hello5(){
        String name = null;
        name.toString();
        return "OK";
    }

}
