package com.yunlong.java.error;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class GlobalExceptionHandler{

    @ExceptionHandler(HttpMessageNotReadableException.class)
    public Object handlerJsonDateException(HttpMessageNotReadableException e){
        System.out.println("ok");
        return null;
    }

    //指定捕捉空指针异常
    @ExceptionHandler(NullPointerException.class)
    public Object handlerNullException(NullPointerException e){
          return 1111;
    }

    //所有异常都会触发此方法，如果有指定的异常会捕捉具体的异常
    @ExceptionHandler(Exception.class)
    public Object handlerException(Exception e){
        return null;
    }
}
